{"": {"diagnostics": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/audioplayers_darwin-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/audioplayers_darwin-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/audioplayers_darwin-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/audioplayers_darwin-master.swiftdeps"}, "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/macos/Classes/AudioContext.swift": {"const-values": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext.d", "diagnostics": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext.dia", "index-unit-output-path": "/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext.o", "llvm-bc": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext.bc", "object": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext.o", "swift-dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/AudioContext~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/macos/Classes/SwiftAudioplayersDarwinPlugin.swift": {"const-values": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin.d", "diagnostics": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin.dia", "index-unit-output-path": "/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin.o", "llvm-bc": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin.bc", "object": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin.o", "swift-dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/SwiftAudioplayersDarwinPlugin~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/macos/Classes/Utils.swift": {"const-values": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils.d", "diagnostics": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils.dia", "index-unit-output-path": "/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils.o", "llvm-bc": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils.bc", "object": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils.o", "swift-dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/Utils~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/macos/Classes/WrappedMediaPlayer.swift": {"const-values": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer.d", "diagnostics": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer.dia", "index-unit-output-path": "/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer.o", "llvm-bc": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer.bc", "object": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer.o", "swift-dependencies": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/Objects-normal/arm64/WrappedMediaPlayer~partial.swiftmodule"}}