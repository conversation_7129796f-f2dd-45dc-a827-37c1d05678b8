Target dependency graph (7 targets)
Target 'Runner' in project 'Runner'
➜ Explicit dependency on target 'Flutter Assemble' in project 'Runner'
➜ Implicit dependency on target 'Pods-Runner' in project 'Pods' via file 'Pods_Runner.framework' in build phase 'Link Binary'
➜ Implicit dependency on target 'audioplayers_darwin' in project 'Pods' via options '-framework audioplayers_darwin' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'path_provider_foundation' in project 'Pods' via options '-framework path_provider_foundation' in build setting 'OTHER_LDFLAGS'
Target 'Pods-Runner' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
➜ Explicit dependency on target 'audioplayers_darwin' in project 'Pods'
➜ Explicit dependency on target 'path_provider_foundation' in project 'Pods'
Target 'Flutter Assemble' in project 'Runner'
➜ Implicit dependency on target 'audioplayers_darwin' in project 'Pods' via options '-framework audioplayers_darwin' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'path_provider_foundation' in project 'Pods' via options '-framework path_provider_foundation' in build setting 'OTHER_LDFLAGS'
Target 'path_provider_foundation' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
➜ Explicit dependency on target 'path_provider_foundation-path_provider_foundation_privacy' in project 'Pods'
Target 'path_provider_foundation-path_provider_foundation_privacy' in project 'Pods' (no dependencies)
Target 'audioplayers_darwin' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
Target 'FlutterMacOS' in project 'Pods' (no dependencies)