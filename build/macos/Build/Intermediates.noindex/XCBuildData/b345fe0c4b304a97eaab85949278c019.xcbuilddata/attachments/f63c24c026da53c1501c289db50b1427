{"case-sensitive": "false", "roots": [{"contents": [{"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Pods/Target Support Files/Pods-Runner/Pods-Runner-umbrella.h", "name": "Pods-Runner-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/Pods_Runner.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/Pods_Runner.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/macos/Classes/AudioplayersDarwinPlugin.h", "name": "AudioplayersDarwinPlugin.h", "type": "file"}, {"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Pods/Target Support Files/audioplayers_darwin/audioplayers_darwin-umbrella.h", "name": "audioplayers_darwin-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/audioplayers_darwin/audioplayers_darwin.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/audioplayers_darwin/audioplayers_darwin.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/audioplayers_darwin/audioplayers_darwin.framework/Headers/audioplayers_darwin-Swift.h", "name": "audioplayers_dar<PERSON>-Swift.h", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/audioplayers_darwin/audioplayers_darwin.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Pods/Target Support Files/path_provider_foundation/path_provider_foundation-umbrella.h", "name": "path_provider_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Headers/path_provider_foundation-Swift.h", "name": "path_provider_foundation-Swift.h", "type": "file"}], "name": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Versions/A/Headers", "type": "directory"}], "version": 0}