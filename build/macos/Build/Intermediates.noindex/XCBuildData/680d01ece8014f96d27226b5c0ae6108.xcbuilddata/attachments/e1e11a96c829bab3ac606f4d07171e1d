-target arm64-apple-macos10.14 '-std=gnu11' -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/ModuleCache.noindex' '-fmodule-name=Pods_Runner' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -g -iquote /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/Pods_Runner-generated-files.hmap -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/Pods_Runner-own-target-headers.hmap -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/Pods_Runner-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-8699adb1dd336b26511df848a716bd42-VFS/all-product-headers.yaml -iquote /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/Pods_Runner-project-headers.hmap -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/include -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/audioplayers_darwin/audioplayers_darwin.framework/Headers -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Headers -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/DerivedSources-normal/arm64 -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/DerivedSources/arm64 -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/DerivedSources -F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug -F/Users/<USER>/development/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64 -F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/audioplayers_darwin -F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation