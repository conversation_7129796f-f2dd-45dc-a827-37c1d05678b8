dependencies: \
  /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/audioplayers_darwin.build/module.modulemap \
  /Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/macos/Classes/AudioplayersDarwinPlugin.m \
  /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Pods/Target\ Support\ Files/audioplayers_darwin/audioplayers_darwin-prefix.pch \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/Cocoa.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c_standard_library.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/ObjectiveC.modulemap \
  /Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/macos/Classes/AudioplayersDarwinPlugin.h \
  /Users/<USER>/development/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64/FlutterMacOS.framework/Modules/module.modulemap \
  /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/audioplayers_darwin/audioplayers_darwin.framework/Headers/audioplayers_darwin-Swift.h
