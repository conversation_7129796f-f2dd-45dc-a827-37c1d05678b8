<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		7zfVkem4L9eyF/Y1+BqaGLsXgNk=
		</data>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<data>
		jnPgnuBglQ7sGAxouGDe7NnNb2Y=
		</data>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<data>
		3jiQJNVei9lmaRFn03zsczczmDg=
		</data>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<data>
		e+9A8sRpgRTnKTmlBs5zDYuhGuk=
		</data>
		<key>Resources/flutter_assets/assets/audio/README.md</key>
		<data>
		9wLhQyOyBtaFcL8T65++T8gkwP0=
		</data>
		<key>Resources/flutter_assets/assets/audio/asmr/placeholder.txt</key>
		<data>
		OtzgAnJdzryXWeJeP7G02WgXLFQ=
		</data>
		<key>Resources/flutter_assets/assets/audio/audio_config.yaml</key>
		<data>
		e0oUOALAeaUQ9i9A9YzqOTKmD54=
		</data>
		<key>Resources/flutter_assets/assets/audio/default/break_end.wav</key>
		<data>
		/sCBeYJZfEToCdXFDZeKvFQT5DA=
		</data>
		<key>Resources/flutter_assets/assets/audio/default/break_start.wav</key>
		<data>
		mS99Wzs3azi+fViL5kZxiGA5ySE=
		</data>
		<key>Resources/flutter_assets/assets/audio/default/placeholder.txt</key>
		<data>
		tQWYx0Ni7twhqCy+kukihLkhgYs=
		</data>
		<key>Resources/flutter_assets/assets/audio/default/session_complete.mp3</key>
		<data>
		wwZilPwln7U/IV3Ynqh0ltM8O0Q=
		</data>
		<key>Resources/flutter_assets/assets/audio/nature/placeholder.txt</key>
		<data>
		mtkZasZbQK6Eo/zt+w5oE869yMU=
		</data>
		<key>Resources/flutter_assets/assets/audio/synthetic/placeholder.txt</key>
		<data>
		5Hde2xci1qHVA2XupTHw752wRBg=
		</data>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Resources/flutter_assets/isolate_snapshot_data</key>
		<data>
		cwnrd7SVzU/+IluNxTgC2ALcd2w=
		</data>
		<key>Resources/flutter_assets/kernel_blob.bin</key>
		<data>
		j8RifZhHAlwX9zHJn081NdHsy9I=
		</data>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VoVnsu58hN7wrwazX0nyAopiB0Q=
		</data>
		<key>Resources/flutter_assets/vm_snapshot_data</key>
		<data>
		ituzZzkouVPX475mGIvTzzeOnk8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CLA7WOT/JcZvXmAZOycoq1JGVjvE8lMhGlxINbvB0zM=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			ssELISNEFADSDJHpGd8gq9zhLaNvTYJz74US//38Pus=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			trg+MIbplVLOtFthNibnNwxLNKVTzVCYcwThpcTgYsY=
			</data>
		</dict>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			zUrZ2zhutsrNv+ZAMeAXUtzWmeKtzFC7B3xSiwUay34=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			kUG6oUsZLG06UbwiSHKZ8qLc+27xRqMpuGdBGwEWvPo=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/asmr/placeholder.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			LDnnz5BsC+xnLxRxg3A60UpEMdX2m/FdDONsJ3hwIAs=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/audio_config.yaml</key>
		<dict>
			<key>hash2</key>
			<data>
			RVcr5BWi5ZE81oFb105cOgRS8AriKbOShHAJ6z1aOEs=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/default/break_end.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			p03b1zG+7vBevA4Shy7jBLiO4s93bO4kC9DoKTzT1Hg=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/default/break_start.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			HA1IrJ+c7e+p3GRjsFZNRUU6yxo3MrQ45ZPDlV5VqDQ=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/default/placeholder.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			AW8l6PLKSIVM9iVSKblI1UuD/XMQam3QqCt3hXyQW4E=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/default/session_complete.mp3</key>
		<dict>
			<key>hash2</key>
			<data>
			kM9OdRPb66yE7izaDZAUIRZ5UH47Yc55Ugn8u3/m6sA=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/nature/placeholder.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ODS40nKt8e2JSRr/3egdyUmFgElb+IHmDpyubQiGAlk=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/audio/synthetic/placeholder.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			nUS2jcD1Z8XBSV+cDPSabufLPs4sjHBkwKPzlHXK9rU=
			</data>
		</dict>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Resources/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			QogPK7LScf8HpyrdsTpkL8WOPl9NnQTGL1N2R05tsZE=
			</data>
		</dict>
		<key>Resources/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			v0wsG3JTpsTm2wIvdi6xbYSKk6Uc/OFu4MY1e0jltkc=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			4PC1vOWKHlNOnW4dGwFfD4TPwBCllniPCsOy7+1X5co=
			</data>
		</dict>
		<key>Resources/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			L8Ll/5LQYd/uiFbt7jD8B17se0Auql4jpiImNFSyl6A=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
