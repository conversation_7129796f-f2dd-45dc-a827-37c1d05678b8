# 音频文件放置说明

## 简化的目录结构

每个主题只需3个音频文件，对应3个关键时间点：

### 默认主题 (default/) - 优先级最高
```
default/
├── break_start.mp3        # 小休息开始
├── break_end.mp3          # 小休息结束
└── session_complete.mp3   # 90分钟阶段结束
```

### 自然音主题 (nature/)
```
nature/
├── water_drop.mp3         # 小休息开始（水滴声）
├── wind_chime.mp3         # 小休息结束（风铃声）
└── forest_birds.mp3       # 阶段结束（森林鸟鸣）
```

### 合成音主题 (synthetic/)
```
synthetic/
├── soft_bell.mp3          # 小休息开始（柔和铃声）
├── clear_ping.mp3         # 小休息结束（清脆提示音）
└── success_melody.mp3     # 阶段结束（成功旋律）
```

### ASMR主题 (asmr/)
```
asmr/
├── soft_whisper.mp3       # 小休息开始（轻柔耳语）
├── keyboard_soft.mp3      # 小休息结束（键盘敲击）
└── completion_whisper.mp3 # 阶段结束（完成耳语）
```

## 音频文件要求

### 技术规格
- **格式**: MP3
- **比特率**: 128kbps
- **采样率**: 44.1kHz
- **最大文件大小**: 500KB

### 时长要求
- **小休息开始/结束**: 1-3秒
- **阶段结束**: 3-8秒

### 音质要求
- 无杂音、无失真
- 音量标准化处理
- 支持淡入淡出效果

## 优先级

### 高优先级（必需）
1. `default/break_start.mp3` - 默认小休息开始音效
2. `default/break_end.mp3` - 默认小休息结束音效
3. `default/session_complete.mp3` - 默认阶段结束音效

### 中优先级（推荐）
- `nature/` 目录下的3个音频文件
- 自然音主题的完整音效集

### 低优先级（可选）
- `synthetic/` 目录下的3个音频文件
- `asmr/` 目录下的3个音频文件

## 注意事项

1. 文件名必须与配置文件中的定义完全一致
2. 确保音频文件具有合法使用权
3. 建议先添加高优先级文件进行测试
4. 可以使用相同的音频文件用于不同事件（通过配置文件映射）

## 测试建议

1. 添加音频文件后运行应用测试播放功能
2. 检查音量是否合适
3. 验证在不同设备上的播放效果
4. 确认音频时长符合用户体验要求
