{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "audioplayers_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_android-5.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.15/", "native_build": true, "dependencies": []}], "macos": [{"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "audioplayers_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_linux-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}], "windows": [{"name": "audioplayers_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_windows-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}], "web": [{"name": "audioplayers_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_web-5.1.0/", "dependencies": []}]}, "dependencyGraph": [{"name": "audioplayers", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_web", "audioplayers_windows", "path_provider"]}, {"name": "audioplayers_android", "dependencies": []}, {"name": "audioplayers_darwin", "dependencies": []}, {"name": "audioplayers_linux", "dependencies": []}, {"name": "audioplayers_web", "dependencies": []}, {"name": "audioplayers_windows", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}], "date_created": "2025-05-30 11:56:54.060156", "version": "3.24.5", "swift_package_manager_enabled": false}