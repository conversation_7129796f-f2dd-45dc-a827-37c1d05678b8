import 'package:flutter/services.dart';
import 'package:yaml/yaml.dart';

/// 简化的音频配置管理类
class AudioConfig {
  static AudioConfig? _instance;
  static AudioConfig get instance => _instance ??= AudioConfig._();

  AudioConfig._();

  Map<String, dynamic>? _config;
  bool _isLoaded = false;

  /// 加载音频配置
  Future<void> loadConfig() async {
    if (_isLoaded) {
      print('AudioConfig: Already loaded, skipping');
      return;
    }

    try {
      print('AudioConfig: Loading audio config from assets/audio/audio_config.yaml');
      final yamlString = await rootBundle.loadString('assets/audio/audio_config.yaml');
      print('AudioConfig: YAML loaded successfully, length: ${yamlString.length} characters');

      final yamlMap = loadYaml(yamlString);
      _config = _convertYamlToMap(yamlMap);
      _isLoaded = true;

      print('AudioConfig: Parsed successfully');
      print('AudioConfig: Available themes: ${_config!['themes']?.keys?.toList()}');
      print('AudioConfig: Default theme: ${_config!['default_theme']}');
      print('AudioConfig: Default volume: ${_config!['default_volume']}');

      // 打印每个主题的详细信息
      final themes = _config!['themes'] as Map<String, dynamic>?;
      if (themes != null) {
        themes.forEach((themeId, themeData) {
          print('AudioConfig: Theme "$themeId": ${themeData}');
        });
      }
    } catch (e) {
      print('AudioConfig: Failed to load audio config: $e');
      throw Exception('Failed to load audio config: $e');
    }
  }

  /// 递归转换 YamlMap 为 Map<String, dynamic>
  Map<String, dynamic> _convertYamlToMap(dynamic yamlData) {
    if (yamlData is YamlMap) {
      final Map<String, dynamic> result = {};
      for (final entry in yamlData.entries) {
        result[entry.key.toString()] = _convertYamlValue(entry.value);
      }
      return result;
    } else if (yamlData is Map) {
      final Map<String, dynamic> result = {};
      for (final entry in yamlData.entries) {
        result[entry.key.toString()] = _convertYamlValue(entry.value);
      }
      return result;
    } else {
      throw Exception('Expected YamlMap or Map, got ${yamlData.runtimeType}');
    }
  }

  /// 递归转换 YAML 值
  dynamic _convertYamlValue(dynamic value) {
    if (value is YamlMap) {
      return _convertYamlToMap(value);
    } else if (value is YamlList) {
      return value.map((item) => _convertYamlValue(item)).toList();
    } else if (value is Map) {
      final Map<String, dynamic> result = {};
      for (final entry in value.entries) {
        result[entry.key.toString()] = _convertYamlValue(entry.value);
      }
      return result;
    } else if (value is List) {
      return value.map((item) => _convertYamlValue(item)).toList();
    } else {
      return value;
    }
  }

  /// 确保配置已加载
  void _ensureLoaded() {
    if (!_isLoaded || _config == null) {
      throw Exception('Audio config not loaded. Call loadConfig() first.');
    }
  }

  /// 获取所有音频主题
  List<AudioTheme> getThemes() {
    _ensureLoaded();
    final themes = <AudioTheme>[];
    final themesData = _config!['themes'] as Map<String, dynamic>;

    for (final entry in themesData.entries) {
      themes.add(AudioTheme.fromMap(entry.key, entry.value));
    }

    return themes;
  }

  /// 根据主题ID获取主题
  AudioTheme? getTheme(String themeId) {
    _ensureLoaded();
    final themesData = _config!['themes'] as Map<String, dynamic>;
    final themeData = themesData[themeId];

    if (themeData == null) return null;
    return AudioTheme.fromMap(themeId, themeData);
  }

  /// 获取默认主题
  String getDefaultTheme() {
    _ensureLoaded();
    return _config!['default_theme'] ?? 'default';
  }

  /// 获取默认音量
  double getDefaultVolume() {
    _ensureLoaded();
    return (_config!['default_volume'] ?? 0.7).toDouble();
  }

  /// 获取音频文件路径
  String? getAudioPath(String themeId, String eventType) {
    print('AudioConfig.getAudioPath: Requesting themeId="$themeId", eventType="$eventType"');

    final theme = getTheme(themeId);
    if (theme == null) {
      print('AudioConfig.getAudioPath: Theme "$themeId" not found');
      return null;
    }

    print('AudioConfig.getAudioPath: Found theme "$themeId": ${theme.name}');

    final relativePath = theme.getAudioPath(eventType);
    if (relativePath == null) {
      print('AudioConfig.getAudioPath: Event type "$eventType" not found in theme "$themeId"');
      return null;
    }

    final fullPath = 'assets/audio/$relativePath';
    print('AudioConfig.getAudioPath: Relative path: "$relativePath" -> Full path: "$fullPath"');
    return fullPath;
  }
}

/// 简化的音频主题模型
class AudioTheme {
  final String id;
  final String name;
  final String breakStart;
  final String breakEnd;
  final String sessionComplete;

  AudioTheme({
    required this.id,
    required this.name,
    required this.breakStart,
    required this.breakEnd,
    required this.sessionComplete,
  });

  factory AudioTheme.fromMap(String id, Map<String, dynamic> map) {
    return AudioTheme(
      id: id,
      name: map['name'] ?? '',
      breakStart: map['break_start'] ?? '',
      breakEnd: map['break_end'] ?? '',
      sessionComplete: map['session_complete'] ?? '',
    );
  }

  /// 根据事件类型获取音频文件路径
  String? getAudioPath(String eventType) {
    print('AudioTheme.getAudioPath: Theme "$id" requesting eventType="$eventType"');
    print('AudioTheme.getAudioPath: Available paths - break_start: "$breakStart", break_end: "$breakEnd", session_complete: "$sessionComplete"');

    String? result;
    switch (eventType) {
      case 'break_start':
        result = breakStart;
        break;
      case 'break_end':
        result = breakEnd;
        break;
      case 'session_complete':
        result = sessionComplete;
        break;
      default:
        result = null;
    }

    print('AudioTheme.getAudioPath: Returning "$result" for eventType="$eventType"');
    return result;
  }
}

/// 音频配置助手类
class AudioConfigHelper {
  /// 获取所有可用的音频主题
  static Future<List<AudioTheme>> getThemes() async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getThemes();
  }

  /// 获取所有主题名称
  static Future<List<String>> getThemeNames() async {
    final themes = await getThemes();
    return themes.map((theme) => theme.name).toList();
  }

  /// 获取所有主题ID
  static Future<List<String>> getThemeIds() async {
    final themes = await getThemes();
    return themes.map((theme) => theme.id).toList();
  }

  /// 根据主题ID获取主题
  static Future<AudioTheme?> getTheme(String themeId) async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getTheme(themeId);
  }

  /// 获取音频文件路径
  static Future<String?> getAudioPath(String themeId, String eventType) async {
    print('AudioConfigHelper.getAudioPath: Called with themeId="$themeId", eventType="$eventType"');
    await AudioConfig.instance.loadConfig();
    final result = AudioConfig.instance.getAudioPath(themeId, eventType);
    print('AudioConfigHelper.getAudioPath: Returning "$result"');
    return result;
  }

  /// 获取默认主题
  static Future<String> getDefaultTheme() async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getDefaultTheme();
  }

  /// 获取默认音量
  static Future<double> getDefaultVolume() async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getDefaultVolume();
  }

  /// 验证音频配置是否有效
  static Future<bool> validateConfig() async {
    try {
      await AudioConfig.instance.loadConfig();
      final themes = AudioConfig.instance.getThemes();
      return themes.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
