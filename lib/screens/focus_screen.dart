import 'package:flutter/material.dart';
import 'dart:async';
import '../utils/constants.dart';
import '../models/focus_session.dart';
import '../models/settings.dart';
import '../services/audio_service.dart';

// 专注计时界面
class FocusScreen extends StatefulWidget {
  final FocusSession session;
  final AppSettings settings;
  final VoidCallback? onPause;
  final VoidCallback? onResume;
  final VoidCallback? onStop;
  final Function(BreakRecord)? onBreakTriggered;
  final VoidCallback? onSessionComplete;

  const FocusScreen({
    super.key,
    required this.session,
    required this.settings,
    this.onPause,
    this.onResume,
    this.onStop,
    this.onBreakTriggered,
    this.onSessionComplete,
  });

  @override
  State<FocusScreen> createState() => _FocusScreenState();
}

class _FocusScreenState extends State<FocusScreen> with TickerProviderStateMixin {
  Timer? _timer;
  Timer? _breakTimer;
  late AnimationController _pulseAnimationController;
  late Animation<double> _pulseAnimation;
  DateTime? _nextBreakTime;
  bool _isBreaking = false;
  int _breakCountdown = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startFocusSession();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _breakTimer?.cancel();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _pulseAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));
    _pulseAnimationController.repeat(reverse: true);
  }

  void _startFocusSession() {
    if (widget.session.state == FocusSessionState.notStarted) {
      widget.session.start();
    }
    _scheduleNextBreak();
    _startTimer();
  }

  void _scheduleNextBreak() {
    final intervalMinutes = widget.settings.generateRandomBreakInterval();
    _nextBreakTime = DateTime.now().add(Duration(minutes: intervalMinutes));
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // 检查 widget 是否仍然 mounted
      if (!mounted) {
        timer.cancel();
        return;
      }

      if (widget.session.state != FocusSessionState.active) return;

      setState(() {
        widget.session.elapsedSeconds++;
        if (widget.session.elapsedSeconds >= 60) {
          widget.session.elapsedMinutes++;
          widget.session.elapsedSeconds = 0;
        }
      });

      // 检查是否需要休息（只在非休息状态下检查）
      if (!_isBreaking && _nextBreakTime != null && DateTime.now().isAfter(_nextBreakTime!)) {
        _triggerBreak();
      }

      // 检查是否完成
      if (widget.session.remainingSeconds <= 0) {
        _completeSession();
      }
    });
  }

  void _triggerBreak() async {
    // 防止重复触发
    if (_isBreaking) return;

    print('FocusScreen._triggerBreak: Starting break');

    setState(() {
      _isBreaking = true;
      _breakCountdown = widget.settings.generateRandomBreakDuration();
    });

    final breakRecord = BreakRecord(
      startTime: DateTime.now(),
      durationSeconds: _breakCountdown,
    );

    widget.session.addBreak(breakRecord);
    widget.onBreakTriggered?.call(breakRecord);

    // 播放小休息开始音效
    try {
      print('FocusScreen._triggerBreak: Playing break_start sound');
      await AudioService.instance.playBreakStartSound(widget.settings);
      print('FocusScreen._triggerBreak: Break_start sound completed');
    } catch (e) {
      print('FocusScreen._triggerBreak: Failed to play break_start sound: $e');
      // 音效播放失败不影响休息流程
    }

    _startBreakTimer();
  }

  void _startBreakTimer() {
    _breakTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // 检查 widget 是否仍然 mounted
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _breakCountdown--;
      });

      if (_breakCountdown <= 0) {
        _endBreak();
      }
    });
  }

  void _endBreak() async {
    print('FocusScreen._endBreak: Ending break');

    _breakTimer?.cancel();
    setState(() {
      _isBreaking = false;
    });

    // 播放小休息结束音效
    try {
      print('FocusScreen._endBreak: Playing break_end sound');
      await AudioService.instance.playBreakEndSound(widget.settings);
      print('FocusScreen._endBreak: Break_end sound completed');
    } catch (e) {
      print('FocusScreen._endBreak: Failed to play break_end sound: $e');
      // 音效播放失败不影响专注流程
    }

    _scheduleNextBreak();
  }

  void _completeSession() async {
    print('FocusScreen._completeSession: Completing session');

    _timer?.cancel();
    widget.session.complete();

    // 播放阶段完成音效
    try {
      print('FocusScreen._completeSession: Playing session_complete sound');
      await AudioService.instance.playSessionCompleteSound(widget.settings);
      print('FocusScreen._completeSession: Session_complete sound completed');
    } catch (e) {
      print('FocusScreen._completeSession: Failed to play session_complete sound: $e');
      // 音效播放失败不影响会话完成流程
    }

    widget.onSessionComplete?.call();
  }

  void _pauseSession() {
    _timer?.cancel();
    widget.session.pause();
    widget.onPause?.call();
  }

  void _resumeSession() {
    widget.session.resume();
    widget.onResume?.call();
    _startTimer();
  }

  void _stopSession() {
    _timer?.cancel();
    _breakTimer?.cancel();
    widget.session.cancel();
    widget.onStop?.call();
  }

  @override
  Widget build(BuildContext context) {
    if (_isBreaking) {
      return _buildBreakScreen();
    }

    return Scaffold(
      backgroundColor: const Color(AppConstants.backgroundColor),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              // 状态标题
              _buildHeader(),

              const SizedBox(height: AppConstants.paddingXLarge),

              // 主要计时器
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildMainTimer(),

                    const SizedBox(height: AppConstants.paddingXLarge),

                    _buildProgressInfo(),

                    const SizedBox(height: AppConstants.paddingXLarge),

                    _buildControlButtons(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBreakScreen() {
    return Scaffold(
      backgroundColor: const Color(AppConstants.backgroundColor),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '休息时间...',
                style: TextStyle(
                  fontSize: AppConstants.titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: const Color(AppConstants.textPrimaryColor),
                ),
              ),

              const SizedBox(height: AppConstants.paddingXLarge),

              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        color: const Color(AppConstants.warningColor),
                        borderRadius: BorderRadius.circular(75),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(AppConstants.warningColor).withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          '$_breakCountdown',
                          style: const TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              Text(
                '剩余秒数',
                style: TextStyle(
                  fontSize: AppConstants.bodyFontSize,
                  color: const Color(AppConstants.textSecondaryColor),
                ),
              ),

              const SizedBox(height: AppConstants.paddingXLarge),

              Container(
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                decoration: BoxDecoration(
                  color: const Color(AppConstants.cardColor),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                ),
                child: Text(
                  '请闭眼深呼吸放松',
                  style: TextStyle(
                    fontSize: AppConstants.bodyFontSize,
                    color: const Color(AppConstants.textPrimaryColor),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Text(
      '专注中...',
      style: TextStyle(
        fontSize: AppConstants.titleFontSize,
        fontWeight: FontWeight.bold,
        color: const Color(AppConstants.textPrimaryColor),
      ),
    );
  }

  Widget _buildMainTimer() {
    return Container(
      width: 250,
      height: 250,
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(125),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${widget.session.remainingMinutes.toString().padLeft(2, '0')}:${widget.session.remainingSecondsOnly.toString().padLeft(2, '0')}',
            style: const TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.bold,
              color: Color(AppConstants.primaryColor),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            '剩余时间(分:秒)',
            style: TextStyle(
              fontSize: AppConstants.captionFontSize,
              color: const Color(AppConstants.textSecondaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Column(
        children: [
          Text(
            '已完成${widget.session.elapsedMinutes}分钟',
            style: TextStyle(
              fontSize: AppConstants.bodyFontSize,
              fontWeight: FontWeight.w600,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            '已休息${widget.session.totalBreaks}次(共${widget.session.totalBreakDuration}秒)',
            style: TextStyle(
              fontSize: AppConstants.captionFontSize,
              color: const Color(AppConstants.textSecondaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Column(
      children: [
        // 暂停/恢复按钮
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: widget.session.state == FocusSessionState.active
                ? _pauseSession
                : _resumeSession,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(AppConstants.secondaryColor),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
            ),
            child: Text(
              widget.session.state == FocusSessionState.active ? '暂停专注' : '继续专注',
              style: const TextStyle(
                fontSize: AppConstants.bodyFontSize,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        // 结束按钮
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: _stopSession,
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(AppConstants.textSecondaryColor),
              side: const BorderSide(
                color: Color(AppConstants.textSecondaryColor),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
            ),
            child: const Text(
              '结束专注',
              style: TextStyle(
                fontSize: AppConstants.bodyFontSize,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
