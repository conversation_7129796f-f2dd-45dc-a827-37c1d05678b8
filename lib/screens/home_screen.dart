import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../models/statistics.dart';
import '../models/settings.dart';
import '../services/audio_service.dart';

// 主页界面
class HomeScreen extends StatefulWidget {
  final StatisticsData? statisticsData;
  final AppSettings? settings;
  final VoidCallback? onStartFocus;

  const HomeScreen({
    super.key,
    this.statisticsData,
    this.settings,
    this.onStartFocus,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _buttonAnimationController;
  late Animation<double> _buttonScaleAnimation;

  @override
  void initState() {
    super.initState();
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _buttonScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _buttonAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _buttonAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(AppConstants.backgroundColor),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              // 标题
              _buildHeader(),

              const SizedBox(height: AppConstants.paddingXLarge),

              // 主要内容区域
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 开始专注按钮
                    _buildStartButton(),

                    const SizedBox(height: AppConstants.paddingXLarge),

                    // 统计信息卡片（如果有历史数据）
                    if (widget.statisticsData != null &&
                        widget.statisticsData!.sessions.isNotEmpty)
                      _buildStatisticsCard(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '间歇流',
          style: TextStyle(
            fontSize: AppConstants.titleFontSize,
            fontWeight: FontWeight.bold,
            color: const Color(AppConstants.textPrimaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildStartButton() {
    return GestureDetector(
      onTapDown: (_) => _buttonAnimationController.forward(),
      onTapUp: (_) => _buttonAnimationController.reverse(),
      onTapCancel: () => _buttonAnimationController.reverse(),
      onTap: () async {
        print('HomeScreen: Start focus button tapped');

        // 播放开始专注音效（break_end音效）
        if (widget.settings != null) {
          try {
            print('HomeScreen: Playing break_end sound with settings: audioTheme="${widget.settings!.audioTheme}", volume=${widget.settings!.soundVolume}');
            await AudioService.instance.playBreakEndSound(widget.settings!);
            print('HomeScreen: Break_end sound playback completed');
          } catch (e) {
            print('HomeScreen: Failed to play start focus sound: $e');
            // 音效播放失败不影响专注会话的开始
          }
        } else {
          print('HomeScreen: No settings available, skipping audio playback');
        }

        print('HomeScreen: Calling onStartFocus callback');
        widget.onStartFocus?.call();
      },
      child: AnimatedBuilder(
        animation: _buttonScaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _buttonScaleAnimation.value,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: const Color(AppConstants.primaryColor),
                borderRadius: BorderRadius.circular(100),
                boxShadow: [
                  BoxShadow(
                    color: const Color(AppConstants.primaryColor).withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Center(
                child: Text(
                  '开始专注',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatisticsCard() {
    final stats = widget.statisticsData!;
    final todayMinutes = stats.getTodayFocusMinutes();
    final weekMinutes = stats.getWeekFocusMinutes();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                '今日',
                _formatDuration(todayMinutes),
                Icons.today,
              ),
              Container(
                width: 1,
                height: 40,
                color: Colors.grey.withOpacity(0.3),
              ),
              _buildStatItem(
                '本周',
                _formatDuration(weekMinutes),
                Icons.calendar_view_week,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(AppConstants.primaryColor),
          size: 24,
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          label,
          style: TextStyle(
            fontSize: AppConstants.captionFontSize,
            color: const Color(AppConstants.textSecondaryColor),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: AppConstants.bodyFontSize,
            fontWeight: FontWeight.bold,
            color: const Color(AppConstants.textPrimaryColor),
          ),
        ),
      ],
    );
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}分钟';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}小时';
      } else {
        return '${hours}小时${remainingMinutes}分钟';
      }
    }
  }
}
